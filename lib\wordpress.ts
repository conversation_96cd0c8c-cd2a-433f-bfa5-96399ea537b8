// WordPress GraphQL configuration
export const WORDPRESS_API_URL = process.env.NEXT_PUBLIC_WORDPRESS_URL || 'https://your-wordpress-site.com/graphql';

// GraphQL queries for WordPress integration
export const GET_PRODUCTS_QUERY = `
  query GetProducts($first: Int = 12) {
    products(first: $first) {
      nodes {
        id
        name
        slug
        description
        price
        image {
          sourceUrl
          altText
        }
        productCategories {
          nodes {
            name
            slug
          }
        }
      }
    }
  }
`;

export const GET_BLOG_POSTS_QUERY = `
  query GetBlogPosts($first: Int = 6) {
    posts(first: $first) {
      nodes {
        id
        title
        slug
        excerpt
        date
        featuredImage {
          node {
            sourceUrl
            altText
          }
        }
        categories {
          nodes {
            name
            slug
          }
        }
      }
    }
  }
`;

// WordPress API client
export async function fetchGraphQL(query: string, variables = {}) {
  try {
    const response = await fetch(WORDPRESS_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query,
        variables,
      }),
    });

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('WordPress GraphQL fetch error:', error);
    return null;
  }
}