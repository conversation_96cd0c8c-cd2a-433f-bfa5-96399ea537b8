'use client';

import { But<PERSON> } from '@/components/ui/button';
import { ArrowRight } from 'lucide-react';
import ProductCard from './ProductCard';
import { mockProducts } from '@/lib/mock-data';

export default function CelebrationSection() {
  const celebrationProducts = mockProducts.filter(product => 
    product.category === 'Celebration'
  );

  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="flex items-center justify-between mb-12">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-gradient-to-r from-pink-500 to-red-500 rounded-full flex items-center justify-center">
              <div className="w-6 h-6 bg-white rounded-full"></div>
            </div>
            <div>
              <h2 className="text-3xl font-bold text-gray-900">CELEBRATION</h2>
              <p className="text-gray-600 mt-1">Make someone's day with our celebratory bouquets.</p>
            </div>
          </div>
          
          <Button 
            variant="ghost" 
            className="text-gray-900 hover:text-orange-500 transition-colors group"
          >
            See All
            <ArrowRight className="ml-2 w-4 h-4 group-hover:translate-x-1 transition-transform" />
          </Button>
        </div>

        {/* Products Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {mockProducts.slice(0, 4).map((product) => (
            <ProductCard key={product.id} product={product} />
          ))}
        </div>
      </div>
    </section>
  );
}