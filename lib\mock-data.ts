// Mock data for development - will be replaced with WordPress GraphQL data
export const mockProducts = [
  {
    id: '1',
    name: 'Spring Celebration Bouquet',
    slug: 'spring-celebration-bouquet',
    price: 45.99,
    image: 'https://images.pexels.com/photos/1153673/pexels-photo-1153673.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    category: 'Celebration',
    description: 'A vibrant mix of seasonal flowers perfect for any celebration'
  },
  {
    id: '2',
    name: 'Elegant Lily Arrangement',
    slug: 'elegant-lily-arrangement',
    price: 52.99,
    image: 'https://images.pexels.com/photos/1187081/pexels-photo-1187081.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    category: 'Premium',
    description: 'Beautiful white and pink lilies in an elegant arrangement'
  },
  {
    id: '3',
    name: 'Pink Dreams Bouquet',
    slug: 'pink-dreams-bouquet',
    price: 38.99,
    image: 'https://images.pexels.com/photos/1322558/pexels-photo-1322558.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    category: 'Romance',
    description: 'Soft pink roses and complementary flowers for romantic moments'
  },
  {
    id: '4',
    name: 'Sunshine Mixed Bouquet',
    slug: 'sunshine-mixed-bouquet',
    price: 41.99,
    image: 'https://images.pexels.com/photos/1586108/pexels-photo-1586108.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
    category: 'Celebration',
    description: 'Bright and cheerful flowers to brighten anyone\'s day'
  }
];

export const mockBlogPosts = [
  {
    id: '1',
    title: 'The Art of Flower Arrangement',
    slug: 'art-of-flower-arrangement',
    excerpt: 'Discover the secrets behind creating stunning floral arrangements that capture hearts and emotions.',
    date: '2024-01-15',
    image: 'https://images.pexels.com/photos/1188669/pexels-photo-1188669.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1'
  },
  {
    id: '2',
    title: 'Seasonal Flower Care Tips',
    slug: 'seasonal-flower-care-tips',
    excerpt: 'Learn how to keep your flowers fresh and beautiful throughout different seasons.',
    date: '2024-01-10',
    image: 'https://images.pexels.com/photos/1234567/pexels-photo-1234567.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1'
  }
];

export const heroImages = [
  {
    id: 1,
    src: 'https://images.pexels.com/photos/1375849/pexels-photo-1375849.jpeg?auto=compress&cs=tinysrgb&w=300&h=300&dpr=1',
    alt: 'Happy child with sunflower',
    shape: 'blue'
  },
  {
    id: 2,
    src: 'https://images.pexels.com/photos/3760529/pexels-photo-3760529.jpeg?auto=compress&cs=tinysrgb&w=300&h=300&dpr=1',
    alt: 'Woman with pink roses',
    shape: 'orange'
  },
  {
    id: 3,
    src: 'https://images.pexels.com/photos/1547813/pexels-photo-1547813.jpeg?auto=compress&cs=tinysrgb&w=300&h=300&dpr=1',
    alt: 'Person holding bouquet',
    shape: 'pink'
  }
];