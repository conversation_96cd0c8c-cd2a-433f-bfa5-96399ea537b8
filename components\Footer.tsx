'use client';

import Link from 'next/link';
import { Facebook, Instagram, Twitter, Mail, Phone, MapPin } from 'lucide-react';

export default function Footer() {
  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Brand */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <span className="text-2xl font-bold">FLOWER</span>
              <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
            </div>
            <p className="text-gray-400 max-w-sm">
              Creating beautiful moments with fresh flowers from the heart. 
              Delivering love, one bouquet at a time.
            </p>
            <div className="flex space-x-4">
              <Link href="#" className="text-gray-400 hover:text-white transition-colors">
                <Facebook className="w-5 h-5" />
              </Link>
              <Link href="#" className="text-gray-400 hover:text-white transition-colors">
                <Instagram className="w-5 h-5" />
              </Link>
              <Link href="#" className="text-gray-400 hover:text-white transition-colors">
                <Twitter className="w-5 h-5" />
              </Link>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li><Link href="/" className="text-gray-400 hover:text-white transition-colors">Home</Link></li>
              <li><Link href="/packages" className="text-gray-400 hover:text-white transition-colors">Packages</Link></li>
              <li><Link href="/best-seller" className="text-gray-400 hover:text-white transition-colors">Best Seller</Link></li>
              <li><Link href="/pricing" className="text-gray-400 hover:text-white transition-colors">Pricing</Link></li>
              <li><Link href="/blog" className="text-gray-400 hover:text-white transition-colors">Blog</Link></li>
            </ul>
          </div>

          {/* Categories */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Categories</h3>
            <ul className="space-y-2">
              <li><Link href="/category/wedding" className="text-gray-400 hover:text-white transition-colors">Wedding Flowers</Link></li>
              <li><Link href="/category/birthday" className="text-gray-400 hover:text-white transition-colors">Birthday Bouquets</Link></li>
              <li><Link href="/category/sympathy" className="text-gray-400 hover:text-white transition-colors">Sympathy Flowers</Link></li>
              <li><Link href="/category/anniversary" className="text-gray-400 hover:text-white transition-colors">Anniversary</Link></li>
              <li><Link href="/category/corporate" className="text-gray-400 hover:text-white transition-colors">Corporate Events</Link></li>
            </ul>
          </div>

          {/* Contact */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Contact Us</h3>
            <ul className="space-y-3">
              <li className="flex items-center space-x-3">
                <Phone className="w-4 h-4 text-orange-500" />
                <span className="text-gray-400">+****************</span>
              </li>
              <li className="flex items-center space-x-3">
                <Mail className="w-4 h-4 text-orange-500" />
                <span className="text-gray-400"><EMAIL></span>
              </li>
              <li className="flex items-center space-x-3">
                <MapPin className="w-4 h-4 text-orange-500" />
                <span className="text-gray-400">123 Flower St, Garden City</span>
              </li>
            </ul>
          </div>
        </div>

        <div className="border-t border-gray-800 mt-12 pt-8 text-center">
          <p className="text-gray-400">
            © 2024 Flower. All rights reserved. Made with ❤️ for flower lovers.
          </p>
        </div>
      </div>
    </footer>
  );
}