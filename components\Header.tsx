'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { ShoppingCart, Menu, X, ChevronDown } from 'lucide-react';

export default function Header() {
  const [isOpen, setIsOpen] = useState(false);
  const [packagesOpen, setPackagesOpen] = useState(false);

  return (
    <header className="bg-white shadow-sm sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <span className="text-2xl font-bold text-gray-900">FLOWER</span>
            <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <Link href="/" className="text-gray-700 hover:text-gray-900 transition-colors">
              Home
            </Link>
            <div className="relative">
              <button
                onClick={() => setPackagesOpen(!packagesOpen)}
                className="flex items-center space-x-1 text-gray-700 hover:text-gray-900 transition-colors"
              >
                <span>Packages</span>
                <ChevronDown className="w-4 h-4" />
              </button>
              {packagesOpen && (
                <div className="absolute top-full left-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-100">
                  <Link href="/packages/wedding" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Wedding Packages</Link>
                  <Link href="/packages/events" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Event Packages</Link>
                  <Link href="/packages/corporate" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">Corporate Packages</Link>
                </div>
              )}
            </div>
            <Link href="/best-seller" className="text-gray-700 hover:text-gray-900 transition-colors">
              Best Seller
            </Link>
            <Link href="/pricing" className="text-gray-700 hover:text-gray-900 transition-colors">
              Pricing
            </Link>
            <Link href="/blog" className="text-gray-700 hover:text-gray-900 transition-colors">
              Blog
            </Link>
          </nav>

          {/* Desktop Actions */}
          <div className="hidden md:flex items-center space-x-4">
            <Button variant="outline" className="rounded-full">
              Contact
            </Button>
            <button className="relative p-2 text-gray-700 hover:text-gray-900 transition-colors">
              <ShoppingCart className="w-6 h-6" />
              <span className="absolute -top-1 -right-1 w-5 h-5 bg-orange-500 text-white text-xs rounded-full flex items-center justify-center">
                0
              </span>
            </button>
          </div>

          {/* Mobile menu button */}
          <button
            onClick={() => setIsOpen(!isOpen)}
            className="md:hidden p-2 text-gray-700"
          >
            {isOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isOpen && (
          <div className="md:hidden border-t border-gray-100">
            <div className="px-2 pt-2 pb-3 space-y-1">
              <Link href="/" className="block px-3 py-2 text-gray-700">Home</Link>
              <Link href="/packages" className="block px-3 py-2 text-gray-700">Packages</Link>
              <Link href="/best-seller" className="block px-3 py-2 text-gray-700">Best Seller</Link>
              <Link href="/pricing" className="block px-3 py-2 text-gray-700">Pricing</Link>
              <Link href="/blog" className="block px-3 py-2 text-gray-700">Blog</Link>
              <Link href="/contact" className="block px-3 py-2 text-gray-700">Contact</Link>
            </div>
          </div>
        )}
      </div>
    </header>
  );
}