'use client';

import { But<PERSON> } from '@/components/ui/button';
import { ArrowRight } from 'lucide-react';
import { heroImages } from '@/lib/mock-data';
import Image from 'next/image';

export default function HeroSection() {
  return (
    <section className="relative min-h-screen bg-gradient-to-br from-gray-50 to-white overflow-hidden">
      {/* Decorative elements */}
      <div className="absolute top-20 right-20 w-8 h-8 bg-pink-200 rounded-full opacity-60 animate-pulse"></div>
      <div className="absolute bottom-40 left-10 w-12 h-12 bg-blue-200 rounded-full opacity-40"></div>
      <div className="absolute top-1/3 right-1/4 w-6 h-6 bg-orange-200 rounded-full opacity-50"></div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <div className="text-center lg:text-left space-y-8">
            <h1 className="text-5xl lg:text-7xl font-bold text-gray-900 leading-tight">
              FLOWERS<br />
              FROM THE<br />
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-pink-500 to-orange-500">
                HEART
              </span>
            </h1>
            
            <p className="text-xl text-gray-600 max-w-md mx-auto lg:mx-0">
              Get the best flower for your loved one and send it to them
            </p>
            
            <Button 
              size="lg" 
              className="bg-black hover:bg-gray-800 text-white rounded-full px-8 py-6 text-lg group transition-all duration-300 hover:scale-105"
            >
              Shop Now
              <ArrowRight className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" />
            </Button>
          </div>

          {/* Hero Images */}
          <div className="relative">
            {/* Blue flower shape - top left */}
            <div className="absolute top-0 left-0 w-80 h-80 opacity-90">
              <div className="relative w-full h-full">
                <div className="absolute inset-0 bg-blue-400 rounded-full transform rotate-45 scale-75"></div>
                <div className="absolute inset-0 bg-blue-400 rounded-full transform -rotate-45 scale-75"></div>
                <div className="absolute inset-4 rounded-full overflow-hidden border-4 border-white shadow-xl">
                  <Image
                    src={heroImages[0].src}
                    alt={heroImages[0].alt}
                    fill
                    className="object-cover"
                  />
                </div>
              </div>
            </div>

            {/* Orange flower shape - bottom left */}
            <div className="absolute bottom-0 left-12 w-72 h-72 opacity-90">
              <div className="relative w-full h-full">
                <div className="absolute inset-0 bg-orange-400 rounded-full transform rotate-45 scale-75"></div>
                <div className="absolute inset-0 bg-orange-500 rounded-full transform -rotate-45 scale-75"></div>
                <div className="absolute inset-4 rounded-full overflow-hidden border-4 border-white shadow-xl">
                  <Image
                    src={heroImages[1].src}
                    alt={heroImages[1].alt}
                    fill
                    className="object-cover"
                  />
                </div>
              </div>
            </div>

            {/* Pink flower shape - right */}
            <div className="absolute top-16 right-0 w-96 h-96 opacity-90">
              <div className="relative w-full h-full">
                <div className="absolute inset-0 bg-pink-400 rounded-full transform rotate-45 scale-75"></div>
                <div className="absolute inset-0 bg-pink-500 rounded-full transform -rotate-45 scale-75"></div>
                <div className="absolute inset-4 rounded-full overflow-hidden border-4 border-white shadow-xl">
                  <Image
                    src={heroImages[2].src}
                    alt={heroImages[2].alt}
                    fill
                    className="object-cover"
                  />
                </div>
              </div>
            </div>

            {/* Decorative small flowers */}
            <div className="absolute top-1/2 right-1/4 w-12 h-12 bg-gray-300 rounded-full opacity-60"></div>
            <div className="absolute bottom-1/4 right-8 w-8 h-8 bg-purple-300 rounded-full opacity-70"></div>
            
            {/* Floating elements */}
            <div className="absolute top-8 right-1/3 animate-bounce">
              <div className="w-16 h-16 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full opacity-80 shadow-lg"></div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}