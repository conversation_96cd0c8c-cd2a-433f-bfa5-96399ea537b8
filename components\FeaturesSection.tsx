'use client';

import { Truck, Shield, Clock, Award } from 'lucide-react';

const features = [
  {
    icon: Truck,
    title: 'Free Delivery',
    description: 'Free delivery on orders over $50'
  },
  {
    icon: Shield,
    title: 'Fresh Guarantee',
    description: '7-day freshness guarantee on all flowers'
  },
  {
    icon: Clock,
    title: 'Same Day Delivery',
    description: 'Order before 2PM for same day delivery'
  },
  {
    icon: Award,
    title: 'Expert Florists',
    description: 'Arrangements crafted by certified florists'
  }
];

export default function FeaturesSection() {
  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <div 
              key={index}
              className="text-center p-6 bg-white rounded-2xl shadow-sm hover:shadow-md transition-shadow"
            >
              <div className="inline-flex items-center justify-center w-16 h-16 bg-orange-100 text-orange-600 rounded-full mb-4">
                <feature.icon className="w-8 h-8" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">{feature.title}</h3>
              <p className="text-gray-600 text-sm">{feature.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}